import abc
import os
import logging

import torch
from omegaconf import DictConfig
import hydra

import wandb

from agents.utils import Scaler, ActionScaler

# A logger for this file
log = logging.getLogger(__name__)


class BaseAgent(abc.ABC):

    def __init__(
            self,
            model: DictConfig,
            trainset: DictConfig,
            valset: DictConfig,
            train_batch_size: int = 512,
            val_batch_size: int = 512,
            num_workers: int = 8,
            device: str = 'cpu',
            epoch: int = 100,
            scale_data: bool = True,
            eval_every_n_epochs: int = 50
    ):

        self.model = hydra.utils.instantiate(model).to(device)

        self.trainset = hydra.utils.instantiate(trainset)
        self.valset = hydra.utils.instantiate(valset)


        # Auto-detect DC training mode to determine shuffle setting
        is_dc_training = self._is_dc_training_mode()
        shuffle_train = not is_dc_training  # DC training requires shuffle=False, others use shuffle=True

        # Configure DataLoader parameters based on num_workers
        dataloader_kwargs = {
            'pin_memory': False,  # 避免额外GPU内存占用
        }
        
        # Only add prefetch_factor if num_workers > 0
        if num_workers > 0:
            dataloader_kwargs['prefetch_factor'] = 2
        
        self.train_dataloader = torch.utils.data.DataLoader(
            self.trainset,
            batch_size=train_batch_size,
            shuffle=True,  # 使用检测到的shuffle设置
            num_workers=num_workers,
            **dataloader_kwargs
        )

        self.test_dataloader = torch.utils.data.DataLoader(
            self.valset,
            batch_size=val_batch_size,
            shuffle=True,
            num_workers=num_workers,
            **dataloader_kwargs
        )

        self.eval_every_n_epochs = eval_every_n_epochs

        self.epoch = epoch

        self.device = device
        self.working_dir = os.getcwd()

        self.scaler = ActionScaler(self.trainset.get_all_actions(), scale_data, device)

        total_params = sum(p.numel() for p in self.model.get_params())

        wandb.log(
            {
                "model parameters": total_params
            }
        )

        log.info("The model has a total amount of {} parameters".format(total_params))

    def _is_dc_training_mode(self):
        """
        检测是否处于DC训练模式。
        DC训练模式需要shuffle=False来保持样本顺序用于对比学习。

        Returns:
            bool: True if in DC training mode, False otherwise
        """
        try:
            # 检查数据集是否启用了DTW对比学习
            if hasattr(self.trainset, 'use_dtw_contrastive') and self.trainset.use_dtw_contrastive:
                log.info("🔍 检测到DTW对比学习模式 (trainset.use_dtw_contrastive=True) - 设置shuffle=False")
                return True

            # 检查模型是否有DC相关属性
            if hasattr(self.model, 'use_dc') and self.model.use_dc:
                log.info("🔍 检测到DC训练模式 (model.use_dc=True) - 设置shuffle=False")
                return True

            if hasattr(self.model, 'use_decoder_dc') and self.model.use_decoder_dc:
                log.info("🔍 检测到DC训练模式 (model.use_decoder_dc=True) - 设置shuffle=False")
                return True

            # 检查模型是否有DC tokens配置
            if hasattr(self.model, 'n_dc_tokens') and self.model.n_dc_tokens > 0:
                log.info(f"🔍 检测到DC训练模式 (n_dc_tokens={self.model.n_dc_tokens}) - 设置shuffle=False")
                return True

            if hasattr(self.model, 'n_decoder_dc_tokens') and self.model.n_decoder_dc_tokens > 0:
                log.info(f"🔍 检测到DC训练模式 (n_decoder_dc_tokens={self.model.n_decoder_dc_tokens}) - 设置shuffle=False")
                return True

            # 检查模型类名是否包含DC
            model_class_name = self.model.__class__.__name__
            if 'DC' in model_class_name:
                log.info(f"🔍 检测到DC训练模式 (模型类名: {model_class_name}) - 设置shuffle=False")
                return True

            log.info("🔍 未检测到DC训练模式 - 设置shuffle=True")
            return False

        except Exception as e:
            log.warning(f"⚠️  检测DC训练模式时出错: {e} - 默认设置shuffle=True")
            return False

    def train(self):

        if self.model.visual_input:
            self.train_vision_agent()
        else:
            self.train_agent()

    @abc.abstractmethod
    def train_agent(self):
        """
        Main method to train the agents on the given train and test data
        """
        pass

    @abc.abstractmethod
    def train_vision_agent(self):
        """
        Main method to train the vision agents on the given train and test data
        """
        pass

    @abc.abstractmethod
    def train_step(self, state: torch.Tensor, action: torch.Tensor):
        """
        Executes a single training step on a mini-batch of data
        """
        pass

    @abc.abstractmethod
    def evaluate(self, state: torch.Tensor, action: torch.Tensor):
        """
        Method for evaluating the model on one batch of data consisting of two tensors
        """
        pass

    @abc.abstractmethod
    def predict(self, state: torch.Tensor) -> torch.Tensor:
        """
        Method for predicting one step with input data
        """
        pass

    @abc.abstractmethod
    def reset(self) -> torch.Tensor:
        """
        Method for resetting the agents
        """
        pass

    def get_scaler(self, scaler: Scaler):
        self.scaler = scaler

    def load_pretrained_model(self, weights_path: str, sv_name=None) -> None:
        """
        Method to load a pretrained model weights inside self.model
        """

        if sv_name is None:
            self.model.load_state_dict(torch.load(os.path.join(weights_path, "model_state_dict.pth")))
        else:
            self.model.load_state_dict(torch.load(os.path.join(weights_path, sv_name)))
        log.info('Loaded pre-trained model parameters')

    def store_model_weights(self, store_path: str, sv_name=None) -> None:
        """
        Store the model weights inside the store path as model_weights.pth
        """

        if sv_name is None:
            torch.save(self.model.state_dict(), os.path.join(store_path, "model_state_dict.pth"))
        else:
            torch.save(self.model.state_dict(), os.path.join(store_path, sv_name))
